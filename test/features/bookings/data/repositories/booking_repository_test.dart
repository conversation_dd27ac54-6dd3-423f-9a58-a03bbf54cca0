import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/bookings/data/repositories/booking_repository_impl.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockVehicleRepository extends Mock implements VehicleRepository {}

class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockPostgrestFilterBuilder extends Mock
    implements PostgrestFilterBuilder {}

void main() {
  late BookingRepositoryImpl repository;
  late MockNetworkInfo mockNetworkInfo;
  late MockVehicleRepository mockVehicleRepository;
  late MockSupabaseClient mockSupabaseClient;

  setUp(() {
    mockNetworkInfo = MockNetworkInfo();
    mockVehicleRepository = MockVehicleRepository();
    mockSupabaseClient = MockSupabaseClient();
    repository = BookingRepositoryImpl(
      networkInfo: mockNetworkInfo,
      vehicleRepository: mockVehicleRepository,
      supabaseClient: mockSupabaseClient,
    );
  });

  group('BookingRepository', () {
    const tBookingId = 'booking_123';
    const tUserId = 'user_123';
    const tVehicleId = 'vehicle_123';
    const tOwnerId = 'owner_123';

    final tVehicle = VehicleModel(
      id: tVehicleId,
      ownerId: tOwnerId,
      type: VehicleType.car,
      make: 'Toyota',
      model: 'Corolla',
      year: 2020,
      licensePlate: 'BA 1 PA 1234',
      color: 'White',
      transmission: TransmissionType.manual,
      fuelType: FuelType.petrol,
      seatingCapacity: 5,
      dailyRate: 2000.0,
      location: VehicleLocation(
        latitude: 27.7172,
        longitude: 85.3240,
        address: 'Kathmandu',
      ),
      description: 'Test vehicle',
      features: const ['AC', 'GPS'],
      images: const ['image1.jpg'],
      isAvailable: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final tBooking = BookingModel(
      id: tBookingId,
      userId: tUserId,
      vehicleId: tVehicleId,
      vehicle: tVehicle,
      startDate: DateTime.now().add(const Duration(days: 1)),
      endDate: DateTime.now().add(const Duration(days: 3)),
      totalAmount: 4000.0,
      status: BookingStatus.pending,
      createdAt: DateTime.now(),
    );

    test('should check if device is online before creating booking', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

      // act
      final result = await repository.createBooking(tBooking);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result,
          equals(Left(NetworkFailure(message: 'No internet connection'))));
    });

    test('should check vehicle availability before creating booking', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select('id')).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('vehicle_id', tVehicleId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.or(any())).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.filter(any(), any(), any()))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.call())
          .thenAnswer((_) async => []); // Vehicle is available

      when(() => mockFilterBuilder.insert(any())).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select()).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.single()).thenAnswer((_) async => {
            'id': tBookingId,
            ...tBooking.toJson(),
          });

      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => tVehicle);

      // act
      final result = await repository.createBooking(tBooking);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, isA<Right>());
    });

    test('should return failure when vehicle is not available', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select('id')).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('vehicle_id', tVehicleId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.or(any())).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.filter(any(), any(), any()))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.call()).thenAnswer((_) async => [
            {'id': 'existing_booking'}
          ]); // Vehicle is not available

      // act
      final result = await repository.createBooking(tBooking);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, isA<Left>());
      expect((result as Left).value, isA<BookingFailure>());
    });

    test('should get booking by ID successfully', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select()).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('id', tBookingId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.single())
          .thenAnswer((_) async => tBooking.toJson());

      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => tVehicle);

      // act
      final result = await repository.getBooking(tBookingId);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, isA<Right>());
    });

    test('should get user bookings successfully', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.select()).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('user_id', tUserId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.order('start_date', ascending: false))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.call())
          .thenAnswer((_) async => [tBooking.toJson()]);

      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => tVehicle);

      // act
      final result = await repository.getUserBookings(tUserId);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, isA<Right>());
      expect((result as Right).value, isA<List<BookingModel>>());
    });

    test('should update booking status successfully', () async {
      // arrange
      const tStatus = 'confirmed';
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.update(any())).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('id', tBookingId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.call()).thenAnswer((_) async => []);

      // act
      final result = await repository.updateBookingStatus(tBookingId, tStatus);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, equals(const Right(null)));
    });

    test('should cancel booking successfully', () async {
      // arrange
      const tReason = 'User cancelled';
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

      final mockFilterBuilder = MockPostgrestFilterBuilder();
      when(() => mockSupabaseClient.from('bookings'))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.update(any())).thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.eq('id', tBookingId))
          .thenReturn(mockFilterBuilder);
      when(() => mockFilterBuilder.call()).thenAnswer((_) async => []);

      // act
      final result = await repository.cancelBooking(tBookingId, tReason);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      expect(result, equals(const Right(null)));
    });

    test('should calculate price correctly', () async {
      // arrange
      final startDate = DateTime.now().add(const Duration(days: 1));
      final endDate = DateTime.now().add(const Duration(days: 3));
      const expectedPrice = 4000.0; // 2 days * 2000 rate

      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => tVehicle);

      // act
      final result =
          await repository.calculatePrice(tVehicleId, startDate, endDate);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, equals(const Right(expectedPrice)));
    });

    test('should return failure when vehicle not found for price calculation',
        () async {
      // arrange
      final startDate = DateTime.now().add(const Duration(days: 1));
      final endDate = DateTime.now().add(const Duration(days: 3));

      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockVehicleRepository.getVehicleById(tVehicleId))
          .thenAnswer((_) async => null);

      // act
      final result =
          await repository.calculatePrice(tVehicleId, startDate, endDate);

      // assert
      verify(() => mockNetworkInfo.isConnected);
      verify(() => mockVehicleRepository.getVehicleById(tVehicleId));
      expect(result, isA<Left>());
      expect((result as Left).value, isA<NotFoundFailure>());
    });
  });
}
